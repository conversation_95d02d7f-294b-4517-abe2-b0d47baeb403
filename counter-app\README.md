# counter-app

Counter micro frontend app that demonstrates shared store and composables usage.

## Features

- Uses shared Pinia store from shell-app
- Integrates toast notifications via shared composable
- Demonstrates micro frontend architecture

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```
