name: Auto Deploy Changed Apps

on:
  push:
    branches:
      - main
      - master

jobs:
  detect-changes:
    name: Detect Changes with Turbo
    runs-on: ubuntu-latest
    environment: production
    outputs:
      shell: ${{ steps.detect.outputs.shell }}
      demo-one: ${{ steps.detect.outputs.demo-one }}
      demo-two: ${{ steps.detect.outputs.demo-two }}
      demo-three: ${{ steps.detect.outputs.demo-three }}
      has_changes: ${{ steps.detect.outputs.has_changes }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Detect changes using Turbo
        id: detect
        run: |
          echo "🔍 Detecting changes with Turbo..."
          
          # Use Turbo to detect changes
          CHANGED=$(npx turbo run build --filter='...[HEAD^1]' --dry=json | jq -r '.tasks[].package' | grep -v "vite-federation-workspace" || true)
          
          if [ -z "$CHANGED" ]; then
            echo "No changes detected"
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "shell=false" >> $GITHUB_OUTPUT
            echo "demo-one=false" >> $GITHUB_OUTPUT
            echo "demo-two=false" >> $GITHUB_OUTPUT
            echo "demo-three=false" >> $GITHUB_OUTPUT
          else
            echo "Changed packages detected:"
            echo "$CHANGED"
            
            echo "has_changes=true" >> $GITHUB_OUTPUT
            
            # Check each app
            if echo "$CHANGED" | grep -q "shell-app"; then
              echo "shell=true" >> $GITHUB_OUTPUT
              echo "✅ shell-app changed"
            else
              echo "shell=false" >> $GITHUB_OUTPUT
            fi
            
            if echo "$CHANGED" | grep -q "demo-one-app"; then
              echo "demo-one=true" >> $GITHUB_OUTPUT
              echo "✅ demo-one-app changed"
            else
              echo "demo-one=false" >> $GITHUB_OUTPUT
            fi
            
            if echo "$CHANGED" | grep -q "demo-two-app"; then
              echo "demo-two=true" >> $GITHUB_OUTPUT
              echo "✅ demo-two-app changed"
            else
              echo "demo-two=false" >> $GITHUB_OUTPUT
            fi
            
            if echo "$CHANGED" | grep -q "demo-three-app"; then
              echo "demo-three=true" >> $GITHUB_OUTPUT
              echo "✅ demo-three-app changed"
            else
              echo "demo-three=false" >> $GITHUB_OUTPUT
            fi
          fi

  deploy-shell:
    name: Deploy Shell App
    needs: detect-changes
    if: needs.detect-changes.outputs.shell == 'true'
    uses: ./.github/workflows/deploy-shell.yml
    secrets: inherit

  deploy-demo-one:
    name: Deploy Demo One App
    needs: detect-changes
    if: needs.detect-changes.outputs.demo-one == 'true'
    uses: ./.github/workflows/deploy-demo-one.yml
    secrets: inherit

  deploy-demo-two:
    name: Deploy Demo Two App
    needs: detect-changes
    if: needs.detect-changes.outputs.demo-two == 'true'
    uses: ./.github/workflows/deploy-demo-two.yml
    secrets: inherit

  deploy-demo-three:
    name: Deploy Demo Three App
    needs: detect-changes
    if: needs.detect-changes.outputs.demo-three == 'true'
    uses: ./.github/workflows/deploy-demo-three.yml
    secrets: inherit

  summary:
    name: Deployment Summary
    needs: [detect-changes, deploy-shell, deploy-demo-one, deploy-demo-two, deploy-demo-three]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Create Summary
        run: |
          echo "## 🚀 Auto Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.detect-changes.outputs.has_changes }}" == "false" ]; then
            echo "### ✅ No changes detected" >> $GITHUB_STEP_SUMMARY
            echo "No deployments needed" >> $GITHUB_STEP_SUMMARY
          else
            echo "### Deployment Status:" >> $GITHUB_STEP_SUMMARY
            
            if [ "${{ needs.detect-changes.outputs.shell }}" == "true" ]; then
              echo "- Shell App: ${{ needs.deploy-shell.result || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
            fi
            
            if [ "${{ needs.detect-changes.outputs.demo-one }}" == "true" ]; then
              echo "- Demo One: ${{ needs.deploy-demo-one.result || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
            fi
            
            if [ "${{ needs.detect-changes.outputs.demo-two }}" == "true" ]; then
              echo "- Demo Two: ${{ needs.deploy-demo-two.result || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
            fi
            
            if [ "${{ needs.detect-changes.outputs.demo-three }}" == "true" ]; then
              echo "- Demo Three: ${{ needs.deploy-demo-three.result || '⏭️ Skipped' }}" >> $GITHUB_STEP_SUMMARY
            fi
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 Production URLs:" >> $GITHUB_STEP_SUMMARY
          echo "- [Shell App](https://shell-app-bice.vercel.app)" >> $GITHUB_STEP_SUMMARY
          echo "- [Demo One](https://demo-one-app.vercel.app)" >> $GITHUB_STEP_SUMMARY
          echo "- [Demo Two](https://demo-two-app.vercel.app)" >> $GITHUB_STEP_SUMMARY
          echo "- [Demo Three](https://demo-three-app.vercel.app)" >> $GITHUB_STEP_SUMMARY