/* Counter App Styles */
* {
  box-sizing: border-box;
}

#counter-app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.counter-app-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.counter-app-header {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #0054C9 0%, #031F3C 100%);
  color: white;
  margin-bottom: 2rem;
  border-radius: 12px;
}

.counter-app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5em;
  font-weight: 600;
}

.counter-app-header p {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}
