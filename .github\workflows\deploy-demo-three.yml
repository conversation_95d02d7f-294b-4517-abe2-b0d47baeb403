name: Deploy Demo Three App

on:
  workflow_dispatch:
  workflow_call:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
    #   - name: Debug Token
    #     run: "echo \"Token length: ${#VERCEL_TOKEN}\""

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Deploy Demo Three App
        run: |
          echo "🚀 Deploying demo-three-app..."
          cd demo-three-app
          DEPLOYMENT_URL=$(vercel --prod --yes --token=$VERCEL_TOKEN)
          echo "Deployment URL: $DEPLOYMENT_URL"
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_DEMO_THREE }}

      - name: Summary
        run: |
          echo "## ✅ Demo Three App Deployed" >> $GITHUB_STEP_SUMMARY
          echo "URL: $DEPLOYMENT_URL" >> $GITHUB_STEP_SUMMARY