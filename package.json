{"name": "vite-federation-workspace", "private": true, "packageManager": "npm@10.9.2", "workspaces": ["shell-app", "demo-one-app", "demo-two-app", "demo-three-app", "counter-app"], "scripts": {"dev": "dotenv -e .env.local -- turbo run dev --parallel", "build": "dotenv -e .env.prod -- turbo run build", "build:dev": "dotenv -e .env.local -- turbo run build", "build:changed": "dotenv -e .env.prod -- turbo run build --filter=\"...[HEAD~1]\"", "preview": "dotenv -e .env.local -- turbo run preview --parallel", "update-env": "node scripts/update-env.js", "clean": "turbo run clean", "install:clean": "npm run clean && rm -rf node_modules package-lock.json && npm install"}, "devDependencies": {"dotenv-cli": "^7.3.0", "turbo": "^2.5.5"}}