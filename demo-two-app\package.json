{"name": "demo-two-app", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview --port 3002", "preview": "vite preview", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@module-federation/vite": "^1.7.1", "fabric": "^5.3.0", "vue": "^3.5.18"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}