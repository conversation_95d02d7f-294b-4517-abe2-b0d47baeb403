{"name": "shell-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "serve": "vite preview", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"fabric": "^5.3.0", "pinia": "^2.1.7", "uuid": "^11.1.0", "vue": "^3.5.18", "vue-router": "^4.2.4"}, "devDependencies": {"@module-federation/vite": "^1.7.1", "@vitejs/plugin-vue": "^6.0.1", "sass": "^1.69.1", "sass-loader": "^13.3.2", "vite": "^7.0.6"}}