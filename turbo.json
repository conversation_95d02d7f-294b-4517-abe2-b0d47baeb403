{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"], "env": ["NODE_ENV", "VITE_SHELL_REMOTE_ENTRY", "VITE_DEMO_ONE_REMOTE_ENTRY", "VITE_DEMO_TWO_REMOTE_ENTRY", "VITE_DEMO_THREE_REMOTE_ENTRY", "VITE_COUNTER_REMOTE_ENTRY"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "VITE_SHELL_REMOTE_ENTRY", "VITE_DEMO_ONE_REMOTE_ENTRY", "VITE_DEMO_TWO_REMOTE_ENTRY", "VITE_DEMO_THREE_REMOTE_ENTRY", "VITE_COUNTER_REMOTE_ENTRY"]}, "preview": {"cache": false, "persistent": true, "dependsOn": ["build"]}, "serve": {"cache": false, "persistent": true, "dependsOn": ["build"]}, "clean": {"cache": false}}}