<template>
  <Teleport to="body">
    <div 
      v-if="hasNotifications" 
      :class="containerClasses"
      :style="containerStyles"
    >
      <TransitionGroup
        name="toast"
        tag="div"
        class="toast-list"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="getToastClasses(notification)"
          class="toast-notification"
        >
          <div class="toast-content">
            <div class="toast-icon" v-html="notification.iconRef"></div>
            <div class="toast-messages">
              <div class="toast-primary">{{ notification.primaryMessage }}</div>
              <div 
                v-if="notification.secondaryMessage" 
                class="toast-secondary"
              >
                {{ notification.secondaryMessage }}
              </div>
            </div>
          </div>
          <button
            v-if="notification.showCloseButton"
            @click="removeNotification(notification)"
            class="toast-close"
            aria-label="Close notification"
          >
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 1L1 13M1 1L13 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import { useToast } from '../composables/useToastComposable.js'

const { notifications, config, removeNotification, hasNotifications } = useToast()

// Computed styles for positioning
const containerClasses = computed(() => {
  const position = config.value.position
  return [
    'toast-container',
    `toast-container--${position}`
  ]
})

const containerStyles = computed(() => {
  const { offset } = config.value
  return {
    '--toast-offset-x': `${offset.x}rem`,
    '--toast-offset-y': `${offset.y}rem`
  }
})

// Get toast-specific classes
const getToastClasses = (notification) => {
  return [
    'toast',
    `toast--${notification.type}`,
    ...notification.notificationClass
  ]
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  max-width: 400px;
  width: 100%;
}

/* Position variants */
.toast-container--top-left {
  top: var(--toast-offset-y, 1rem);
  left: var(--toast-offset-x, 1rem);
}

.toast-container--top {
  top: var(--toast-offset-y, 1rem);
  left: 50%;
  transform: translateX(-50%);
}

.toast-container--top-right {
  top: var(--toast-offset-y, 1rem);
  right: var(--toast-offset-x, 1rem);
}

.toast-container--bottom-left {
  bottom: var(--toast-offset-y, 1rem);
  left: var(--toast-offset-x, 1rem);
}

.toast-container--bottom {
  bottom: var(--toast-offset-y, 1rem);
  left: 50%;
  transform: translateX(-50%);
}

.toast-container--bottom-right {
  bottom: var(--toast-offset-y, 1rem);
  right: var(--toast-offset-x, 1rem);
}

.toast-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.toast-notification {
  pointer-events: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  min-width: 300px;
  max-width: 400px;
}

.toast-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.toast-messages {
  flex: 1;
  min-width: 0;
}

.toast-primary {
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.toast-secondary {
  font-size: 0.8125rem;
  line-height: 1.25;
  color: #6b7280;
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  color: #9ca3af;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* Toast type variants */
.toast--success {
  border-left: 4px solid #10b981;
}

.toast--error {
  border-left: 4px solid #ef4444;
}

.toast--warning {
  border-left: 4px solid #f59e0b;
}

.toast--info {
  border-left: 4px solid #3b82f6;
}

/* Transitions */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* Responsive */
@media (max-width: 640px) {
  .toast-container {
    max-width: calc(100vw - 2rem);
    left: 1rem !important;
    right: 1rem !important;
    transform: none !important;
  }
  
  .toast-notification {
    min-width: auto;
    max-width: none;
  }
}
</style>
