@use './variables' as *;
@use './typology';
@use './mixins' as *;
@import url('https://fonts.googleapis.com/css2?family=Unbounded:wght@200..900&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');

* {
  box-sizing: border-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

html,
body,
#shell-app {
  height: 100%;
  width: 100%;
}

body {
  color: $color-black;
  padding: 0;
  margin: 0;
}

.image-wrapper-fit-parent,
.iwfp {
  svg {
    height: 100% !important;
    width: 100% !important;
  }

  img {
    height: 100% !important;
    width: 100% !important;
    object-fit: contain;
  }
}

.image-wrapper-fill-width,
.iwfw {
  svg {
    height: auto !important;
    width: 100% !important;
  }

  img {
    height: auto !important;
    width: 100% !important;
  }
}

.image-wrapper-fill-height,
.iwfh {
  svg {
    height: 100% !important;
    width: auto !important;
  }

  img {
    height: 100% !important;
    width: auto !important;
  }
}

.image-wrapper-preserve-aspect-ratio,
.iwpar {
  svg {
    height: auto !important;
    width: auto !important;
    max-height: 100% !important;
    max-width: 100% !important;
  }

  img {
    height: auto !important;
    width: auto !important;
    max-height: 100% !important;
    max-width: 100% !important;
  }
}

.capitalize {
  text-transform: capitalize;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}
